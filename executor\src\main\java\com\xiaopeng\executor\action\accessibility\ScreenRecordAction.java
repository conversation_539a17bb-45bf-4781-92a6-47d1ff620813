package com.xiaopeng.executor.action.accessibility;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.xiaopeng.executor.action.ActionContext;
import com.xiaopeng.executor.action.ActionException;
import com.xiaopeng.executor.action.BaseAction;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.service.ScreenRecordManager;

import java.io.File;

/**
 * 屏幕录制Action
 * 支持多种录屏方式：MediaProjection API、shell命令等
 */
public class ScreenRecordAction extends BaseAction {
    private static final String TAG = "ScreenRecordAction";
    
    // 静态实例，在整个测试过程中保持
    private static ScreenRecordManager sRecordManager;
    
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String action = context.getStringParam("action");
        if (action == null || action.isEmpty()) {
            throw new ActionException("Action parameter is required (start/stop)", FailureCode.SI001);
        }
        
        switch (action.toLowerCase()) {
            case "start":
                return startRecording(context);
            case "stop":
                return stopRecording(context);
            default:
                throw new ActionException("Invalid action: " + action + ". Use 'start' or 'stop'", FailureCode.SI001);
        }
    }
    
    /**
     * 开始录屏
     */
    private TestResult startRecording(ActionContext context) {
        try {
            String duration = context.getStringParam("duration", "30"); // 默认30秒
            String fileName = context.getStringParam("fileName"); // 可选的文件名
            
            int durationSeconds;
            try {
                durationSeconds = Integer.parseInt(duration);
                if (durationSeconds <= 0) {
                    durationSeconds = 30; // 默认值
                }
            } catch (NumberFormatException e) {
                FileLogger.w(TAG, "Invalid duration: " + duration + ", using default 30s");
                durationSeconds = 30;
            }
            
            // 方法1: 尝试使用MediaProjection API (推荐)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                TestResult mediaProjectionResult = startRecordingWithMediaProjection(fileName, durationSeconds);
                if (mediaProjectionResult.isSuccess()) {
                    return mediaProjectionResult;
                }
                FileLogger.w(TAG, "MediaProjection recording failed, trying shell command");
            }
            
            // 方法2: fallback到shell命令
            return startRecordingWithShellCommand(fileName, durationSeconds);
            
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to start recording", e);
            return TestResult.failure("Failed to start recording: " + e.getMessage());
        }
    }
    
    /**
     * 使用MediaProjection API录屏
     */
    private TestResult startRecordingWithMediaProjection(String fileName, int durationSeconds) {
        try {
            // 检查是否已经初始化了ScreenRecordManager
            if (sRecordManager == null) {
                return TestResult.failure("ScreenRecordManager not initialized. MediaProjection permission may not be granted.");
            }
            
            if (sRecordManager.isRecording()) {
                return TestResult.failure("Already recording. Stop current recording first.");
            }
            
            // 生成文件名
            if (fileName == null || fileName.isEmpty()) {
                fileName = "screen_record_" + System.currentTimeMillis() + ".mp4";
            } else if (!fileName.endsWith(".mp4")) {
                fileName += ".mp4";
            }
            
            boolean success = sRecordManager.startRecording(fileName, durationSeconds);
            if (success) {
                String outputPath = sRecordManager.getCurrentOutputPath();
                FileLogger.i(TAG, "MediaProjection recording started: " + outputPath);
                return TestResult.success("Screen recording started (MediaProjection): " + outputPath);
            } else {
                return TestResult.failure("Failed to start MediaProjection recording");
            }
            
        } catch (Exception e) {
            FileLogger.e(TAG, "MediaProjection recording error", e);
            return TestResult.failure("MediaProjection recording error: " + e.getMessage());
        }
    }
    
    /**
     * 使用shell命令录屏
     */
    private TestResult startRecordingWithShellCommand(String fileName, int durationSeconds) {
        try {
            // 创建输出目录
            File outputDir = new File(Constant.AUTOTEST_VIDEO_PATH);
            if (!outputDir.exists() && !outputDir.mkdirs()) {
                FileLogger.e(TAG, "Failed to create output directory: " + outputDir.getAbsolutePath());
                return TestResult.failure("Failed to create output directory");
            }
            
            // 生成文件名和路径
            if (fileName == null || fileName.isEmpty()) {
                fileName = "screen_record_" + System.currentTimeMillis() + ".mp4";
            } else if (!fileName.endsWith(".mp4")) {
                fileName += ".mp4";
            }
            
            String outputPath = new File(outputDir, fileName).getAbsolutePath();
            
            // 尝试多种shell命令方式
            String[] commands = {
                // 方法1: 直接执行screenrecord
                String.format("screenrecord --time-limit %d %s", durationSeconds, outputPath),
                
                // 方法2: 使用/system/bin/路径
                String.format("/system/bin/screenrecord --time-limit %d %s", durationSeconds, outputPath),
                
                // 方法3: 后台执行
                String.format("screenrecord --time-limit %d %s >/dev/null 2>&1 &", durationSeconds, outputPath)
            };
            
            for (int i = 0; i < commands.length; i++) {
                String command = commands[i];
                FileLogger.i(TAG, "Trying command " + (i + 1) + ": " + command);
                
                CMDUtils.CMD_Result result = CMDUtils.runCMD(command, true, true);
                
                if (result != null && result.resultCode == 0) {
                    FileLogger.i(TAG, "Shell recording started successfully: " + outputPath);
                    return TestResult.success("Screen recording started (shell): " + outputPath);
                } else {
                    String error = result != null ? result.error : "null result";
                    FileLogger.w(TAG, "Command " + (i + 1) + " failed: " + error);
                }
            }
            
            return TestResult.failure("All shell command methods failed. Device may not support screenrecord or lacks permissions.");
            
        } catch (Exception e) {
            FileLogger.e(TAG, "Shell command recording error", e);
            return TestResult.failure("Shell command recording error: " + e.getMessage());
        }
    }
    
    /**
     * 停止录屏
     */
    private TestResult stopRecording(ActionContext context) {
        try {
            boolean stoppedMediaProjection = false;
            boolean stoppedShellCommand = false;
            
            // 停止MediaProjection录屏
            if (sRecordManager != null && sRecordManager.isRecording()) {
                sRecordManager.stopRecording();
                stoppedMediaProjection = true;
                FileLogger.i(TAG, "MediaProjection recording stopped");
            }
            
            // 停止shell命令录屏
            CMDUtils.CMD_Result result = CMDUtils.runCMD("pkill screenrecord", true, true);
            if (result != null && result.resultCode == 0) {
                stoppedShellCommand = true;
                FileLogger.i(TAG, "Shell screenrecord processes killed");
            }
            
            if (stoppedMediaProjection || stoppedShellCommand) {
                return TestResult.success("Screen recording stopped");
            } else {
                return TestResult.success("No active recording found to stop");
            }
            
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to stop recording", e);
            return TestResult.failure("Failed to stop recording: " + e.getMessage());
        }
    }
    
    /**
     * 设置ScreenRecordManager实例
     * 这个方法应该在获得MediaProjection权限后调用
     */
    public static void setScreenRecordManager(ScreenRecordManager recordManager) {
        sRecordManager = recordManager;
        FileLogger.i(TAG, "ScreenRecordManager set");
    }
    
    /**
     * 获取ScreenRecordManager实例
     */
    public static ScreenRecordManager getScreenRecordManager() {
        return sRecordManager;
    }
    
    /**
     * 清理资源
     */
    public static void cleanup() {
        if (sRecordManager != null) {
            sRecordManager.release();
            sRecordManager = null;
            FileLogger.i(TAG, "ScreenRecordManager cleaned up");
        }
    }
    
    /**
     * 检查设备是否支持screenrecord命令
     */
    public static boolean isShellScreenRecordSupported() {
        CMDUtils.CMD_Result result = CMDUtils.runCMD("which screenrecord", false, true);
        return result != null && result.resultCode == 0 && 
               result.success != null && result.success.contains("screenrecord");
    }
    
    /**
     * 获取设备录屏能力信息
     */
    public static String getRecordingCapabilities() {
        StringBuilder capabilities = new StringBuilder();
        
        // 检查Android版本
        capabilities.append("Android API: ").append(Build.VERSION.SDK_INT);
        
        // 检查MediaProjection支持
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            capabilities.append(", MediaProjection: supported");
        } else {
            capabilities.append(", MediaProjection: not supported");
        }
        
        // 检查shell命令支持
        if (isShellScreenRecordSupported()) {
            capabilities.append(", Shell screenrecord: supported");
        } else {
            capabilities.append(", Shell screenrecord: not supported");
        }
        
        return capabilities.toString();
    }
}
