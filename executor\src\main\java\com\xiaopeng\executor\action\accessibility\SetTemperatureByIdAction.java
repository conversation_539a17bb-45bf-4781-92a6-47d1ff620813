package com.xiaopeng.executor.action.accessibility;

import android.graphics.Rect;
import android.os.SystemClock;
import android.view.accessibility.AccessibilityNodeInfo;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SetTemperatureByIdAction extends BaseAction {
    private static final String TAG = "SetTemperatureByIdAction";

    private static final int MAX_ATTEMPTS = 30;
    private static final long SWIPE_DELAY_MS = 600;
    private static final int SWIPE_DURATION_MS = 200;
    private static final int MIN_PIXEL_SWIPE = 4;

    // 当前温度与预期温度差值，对应6级滑动步长
    private static final int VERY_LARGE_SWIPE_PERCENTAGE = 60;
    private static final float VERY_LARGE_DIFF_THRESHOLD = 6.0f;

    private static final int LARGE_SWIPE_PERCENTAGE = 50;
    private static final float LARGE_DIFF_THRESHOLD = 4.0f; 

    private static final int MEDIUM_LARGE_SWIPE_PERCENTAGE = 20; 
    private static final float MEDIUM_LARGE_DIFF_THRESHOLD = 2.0f;

    private static final int MEDIUM_SWIPE_PERCENTAGE = 10;
    private static final float MEDIUM_DIFF_THRESHOLD = 1.0f;

    private static final int SMALL_SWIPE_PERCENTAGE = 5;
    private static final float SMALL_DIFF_THRESHOLD = 0.5f;

    private static final int VERY_SMALL_SWIPE_PERCENTAGE = 5;

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String controlResId = context.getStringParam();
        String targetTempStr = context.getStringParam();

        if (controlResId == null || controlResId.isEmpty() || targetTempStr == null || targetTempStr.isEmpty()) {
            String message = "Control resource ID or target temperature is null or empty!";
            FileLogger.e(TAG, message);
            throw new ActionException(message, FailureCode.SI001);
        }

        // 检查是否为特殊温度设置（LO或HI）
        boolean isSpecialTemp = isSpecialTemperature(targetTempStr);
        String normalizedTarget = isSpecialTemp ? targetTempStr.trim().toUpperCase() : null;

        float targetNumeric = -1000f;
        if (!isSpecialTemp) {
            targetNumeric = parseFloatTemp(targetTempStr);
            if (targetNumeric == -1000f) {
                String message = "Target temperature string is invalid: " + targetTempStr;
                FileLogger.e(TAG, message);
                throw new ActionException(message, FailureCode.SI001);
            }
        }

        if (service == null) {
            String message = "Service is not initialized!";
            FileLogger.e(TAG, message);
            throw new ActionException(message, FailureCode.AB002);
        }

        int screenWidth = this.service.getScreenWidth();
        int screenHeight = this.service.getScreenHeight();
        if (screenWidth <= 0 || screenHeight <= 0) {
            String message = "Invalid screen dimensions: " + screenWidth + "x" + screenHeight;
            FileLogger.e(TAG, message);
            throw new ActionException(message, FailureCode.AB001);
        }

        for (int attempt = 0; attempt < MAX_ATTEMPTS; attempt++) {
            AccessibilityNodeInfo controlNodeForReading = this.service.findNode("id", controlResId);
            if (controlNodeForReading == null || controlNodeForReading.getText() == null) {
                String message = "Control node not found or has no text: " + controlResId;
                FileLogger.e(TAG, message);
                this.service.safeRecycle(controlNodeForReading);
                FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
                return TestResult.failure(message);
            }
            String currentTempTextRaw = controlNodeForReading.getText().toString();
            this.service.safeRecycle(controlNodeForReading);

            String currentTempText = currentTempTextRaw.replace("°", "").trim();

            // 检查目标是否已达到
            if (isSpecialTemp) {
                // 特殊温度比较
                if (normalizedTarget.equals(currentTempText.toUpperCase())) {
                    FileLogger.i(TAG, "Target special temperature reached: " + normalizedTarget);
                    return TestResult.success("Target special temperature reached: " + normalizedTarget);
                }
            } else {
                // 数字温度比较
                float currentNumeric = parseFloatTemp(currentTempText);
                if (currentNumeric == -1000f) {
                    FileLogger.w(TAG, "Could not parse current temp text: '" + currentTempTextRaw + "'. Attempt: " + (attempt + 1));
                    SystemClock.sleep(SWIPE_DELAY_MS);
                    continue;
                }
                if (isTargetReached(currentNumeric, targetNumeric)) {
                    FileLogger.i(TAG, "Target temperature reached: Current " + currentNumeric + " matches target " + targetNumeric);
                    return TestResult.success("Target temperature reached: " + targetNumeric);
                }
            }

            AccessibilityNodeInfo controlNodeForSwipe = this.service.findNode("id", controlResId);
            if (controlNodeForSwipe == null) {
                String message = "Control node for swipe not found: " + controlResId;
                FileLogger.e(TAG, message);
                FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
                return TestResult.failure(message);
            }
            Rect controlBounds = new Rect();
            controlNodeForSwipe.getBoundsInScreen(controlBounds);
            this.service.safeRecycle(controlNodeForSwipe);

            if (controlBounds.width() <= 0) {
                String message = "Control node width is zero or invalid: " + controlResId;
                 FileLogger.e(TAG, message);
                 FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
                 return TestResult.failure(message);
            }

            int controlCenterX = controlBounds.centerX();
            int controlCenterY = controlBounds.centerY();
            boolean swipePerformed = false;

            if (isSpecialTemp) {
                // 特殊温度滑动：使用大幅度滑动
                int swipeDistancePixels = (controlBounds.width() * VERY_LARGE_SWIPE_PERCENTAGE) / 100;
                swipeDistancePixels = Math.max(MIN_PIXEL_SWIPE, swipeDistancePixels);

                if ("LO".equals(normalizedTarget)) {
                    // 向左滑动到最低温度
                    swipePerformed = this.service.swipeAbs(controlCenterX, controlCenterY,
                        controlCenterX - swipeDistancePixels, controlCenterY, SWIPE_DURATION_MS);
                } else if ("HI".equals(normalizedTarget)) {
                    // 向右滑动到最高温度
                    swipePerformed = this.service.swipeAbs(controlCenterX, controlCenterY,
                        controlCenterX + swipeDistancePixels, controlCenterY, SWIPE_DURATION_MS);
                }
            } else {
                // 数字温度滑动：根据差值计算滑动距离
                float currentNumeric = parseFloatTemp(currentTempText);
                float diff = Math.abs(currentNumeric - targetNumeric);
                int chosenSwipePercentage = getChosenSwipePercentage(diff);
                int swipeDistancePixels = Math.max(MIN_PIXEL_SWIPE,
                    (controlBounds.width() * chosenSwipePercentage) / 100);

                if (targetNumeric > currentNumeric) {
                    swipePerformed = this.service.swipeAbs(controlCenterX, controlCenterY,
                        controlCenterX + swipeDistancePixels, controlCenterY, SWIPE_DURATION_MS);
                } else if (targetNumeric < currentNumeric) {
                    swipePerformed = this.service.swipeAbs(controlCenterX, controlCenterY,
                        controlCenterX - swipeDistancePixels, controlCenterY, SWIPE_DURATION_MS);
                }
            }

            if (swipePerformed) {
                SystemClock.sleep(SWIPE_DELAY_MS);
            }
        }
        String targetDesc = isSpecialTemp ? normalizedTarget : String.valueOf(targetNumeric);
        String message = "Failed to set temperature to " + targetDesc + " after max attempts.";
        FileLogger.e(TAG, message);
        FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
        return TestResult.failure(message);
    }

    private static int getChosenSwipePercentage(float diff) {
        int chosenSwipePercentage;

        if (diff > VERY_LARGE_DIFF_THRESHOLD) {
            chosenSwipePercentage = VERY_LARGE_SWIPE_PERCENTAGE;
        } else if (diff > LARGE_DIFF_THRESHOLD) {
            chosenSwipePercentage = LARGE_SWIPE_PERCENTAGE;
        } else if (diff > MEDIUM_LARGE_DIFF_THRESHOLD) {
            chosenSwipePercentage = MEDIUM_LARGE_SWIPE_PERCENTAGE;
        } else if (diff > MEDIUM_DIFF_THRESHOLD) {
            chosenSwipePercentage = MEDIUM_SWIPE_PERCENTAGE;
        } else if (diff > SMALL_DIFF_THRESHOLD) {
            chosenSwipePercentage = SMALL_SWIPE_PERCENTAGE;
        } else {
            chosenSwipePercentage = VERY_SMALL_SWIPE_PERCENTAGE;
        }
        return chosenSwipePercentage;
    }

    private boolean isTargetReached(float currentNumericValue, float targetNumericValue) {
        if (currentNumericValue == -1000f) return false;
        return currentNumericValue == targetNumericValue;
    }

    private float parseFloatTemp(String tempText) {
        if (tempText == null || tempText.isEmpty()) return -1000f;
        String normalizedText = tempText.replace("°", "").trim();
        try {
            return Float.parseFloat(normalizedText);
        } catch (NumberFormatException e) {
            FileLogger.w(TAG, "Cannot parse temperature string: '" + normalizedText + "'. Returning -1000f.");
            return -1000f;
        }
    }

    /**
     * 检查是否为特殊温度设置（LO或HI）
     */
    private boolean isSpecialTemperature(String tempStr) {
        if (tempStr == null) return false;
        String normalized = tempStr.trim().toUpperCase();
        return "LO".equals(normalized) || "HI".equals(normalized);
    }
}