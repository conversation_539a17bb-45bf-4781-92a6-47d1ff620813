package com.xiaopeng.xpautotest.demo;

import android.content.Context;

import com.xiaopeng.executor.action.ActionContext;
import com.xiaopeng.executor.action.accessibility.ScreenRecordAction;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.helper.ScreenRecordHelper;

/**
 * 录屏功能演示类
 * 展示如何在测试中使用录屏功能
 */
public class ScreenRecordDemo {
    private static final String TAG = "ScreenRecordDemo";
    
    /**
     * 演示基本录屏功能
     */
    public static void demoBasicRecording(Context context) {
        FileLogger.i(TAG, "=== 基本录屏演示 ===");
        
        try {
            // 1. 获取录屏辅助类
            ScreenRecordHelper helper = ScreenRecordHelper.getInstance(context);
            
            // 2. 检查录屏能力
            String capabilities = helper.getCapabilitiesInfo();
            FileLogger.i(TAG, "录屏能力信息:\n" + capabilities);
            
            // 3. 检查权限
            if (!helper.hasScreenRecordPermission()) {
                FileLogger.w(TAG, "没有录屏权限，需要先在Activity中请求权限");
                return;
            }
            
            // 4. 开始录屏
            boolean success = helper.startRecording("demo_recording.mp4", 10);
            if (success) {
                FileLogger.i(TAG, "录屏开始成功");
                
                // 模拟一些操作
                Thread.sleep(5000);
                
                // 5. 停止录屏
                helper.stopRecording();
                FileLogger.i(TAG, "录屏停止");
                
                String outputPath = helper.getCurrentOutputPath();
                FileLogger.i(TAG, "录屏文件保存在: " + outputPath);
            } else {
                FileLogger.e(TAG, "录屏开始失败");
            }
            
        } catch (Exception e) {
            FileLogger.e(TAG, "录屏演示出错", e);
        }
    }
    
    /**
     * 演示使用Action进行录屏
     */
    public static void demoActionRecording() {
        FileLogger.i(TAG, "=== Action录屏演示 ===");
        
        try {
            ScreenRecordAction action = new ScreenRecordAction();
            
            // 1. 开始录屏
            ActionContext startContext = new ActionContext();
            startContext.putParam("action", "start");
            startContext.putParam("duration", "15");
            startContext.putParam("fileName", "action_demo_recording");
            
            TestResult startResult = action.execute(startContext);
            FileLogger.i(TAG, "开始录屏结果: " + startResult.getMessage());
            
            if (startResult.isSuccess()) {
                // 模拟测试操作
                Thread.sleep(8000);
                
                // 2. 停止录屏
                ActionContext stopContext = new ActionContext();
                stopContext.putParam("action", "stop");
                
                TestResult stopResult = action.execute(stopContext);
                FileLogger.i(TAG, "停止录屏结果: " + stopResult.getMessage());
            }
            
        } catch (Exception e) {
            FileLogger.e(TAG, "Action录屏演示出错", e);
        }
    }
    
    /**
     * 演示录屏错误处理
     */
    public static void demoErrorHandling(Context context) {
        FileLogger.i(TAG, "=== 错误处理演示 ===");
        
        try {
            ScreenRecordHelper helper = ScreenRecordHelper.getInstance(context);
            
            // 1. 测试重复开始录屏
            helper.startRecording("test1.mp4", 5);
            boolean duplicateStart = helper.startRecording("test2.mp4", 5);
            FileLogger.i(TAG, "重复开始录屏结果: " + duplicateStart);
            
            // 2. 测试停止不存在的录屏
            helper.stopRecording();
            helper.stopRecording(); // 再次停止
            
            // 3. 测试无权限情况
            helper.resetPermission();
            boolean noPermissionStart = helper.startRecording("test3.mp4", 5);
            FileLogger.i(TAG, "无权限录屏结果: " + noPermissionStart);
            
        } catch (Exception e) {
            FileLogger.e(TAG, "错误处理演示出错", e);
        }
    }
    
    /**
     * 演示在测试脚本中集成录屏
     */
    public static void demoTestIntegration() {
        FileLogger.i(TAG, "=== 测试集成演示 ===");
        
        try {
            ScreenRecordAction recordAction = new ScreenRecordAction();
            
            // 测试场景：登录流程录屏
            FileLogger.i(TAG, "开始登录流程测试录屏");
            
            // 1. 开始录屏
            ActionContext startContext = new ActionContext();
            startContext.putParam("action", "start");
            startContext.putParam("duration", "30");
            startContext.putParam("fileName", "login_test_" + System.currentTimeMillis());
            
            TestResult startResult = recordAction.execute(startContext);
            if (!startResult.isSuccess()) {
                FileLogger.e(TAG, "录屏开始失败: " + startResult.getMessage());
                return;
            }
            
            // 2. 执行测试步骤（这里用sleep模拟）
            FileLogger.i(TAG, "执行测试步骤...");
            Thread.sleep(3000);
            
            // 3. 测试完成，停止录屏
            ActionContext stopContext = new ActionContext();
            stopContext.putParam("action", "stop");
            
            TestResult stopResult = recordAction.execute(stopContext);
            FileLogger.i(TAG, "录屏停止结果: " + stopResult.getMessage());
            
            FileLogger.i(TAG, "登录流程测试录屏完成");
            
        } catch (Exception e) {
            FileLogger.e(TAG, "测试集成演示出错", e);
        }
    }
    
    /**
     * 运行所有演示
     */
    public static void runAllDemos(Context context) {
        FileLogger.i(TAG, "开始运行所有录屏演示");
        
        // 基本录屏演示
        demoBasicRecording(context);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Action录屏演示
        demoActionRecording();
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 错误处理演示
        demoErrorHandling(context);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试集成演示
        demoTestIntegration();
        
        FileLogger.i(TAG, "所有录屏演示完成");
    }
}
