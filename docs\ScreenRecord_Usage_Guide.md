# 屏幕录制功能使用指南

## 概述

本项目提供了完整的屏幕录制解决方案，解决了Android中使用shell命令执行screenrecord权限不足的问题。

## 问题背景

原始问题：
```
java.io.IOException: Cannot run program "su": error=13, Permission denied
```

这是因为普通Android应用无法获得root权限执行`su`命令。

## 解决方案

### 1. MediaProjection API（推荐）

使用Android官方的MediaProjection API进行录屏，无需root权限。

**优势：**
- 官方支持，稳定可靠
- 无需特殊权限
- 支持Android 5.0+
- 高质量录制

### 2. Shell命令（备用）

作为fallback方案，在某些特殊环境下仍可使用shell命令。

## 使用方法

### 1. 权限配置

在`AndroidManifest.xml`中已包含必要权限：
```xml
<uses-permission android:name="android.permission.MEDIA_PROJECTION" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

### 2. 在Activity中初始化

```java
public class MainActivity extends XActivity {
    private ScreenRecordHelper mScreenRecordHelper;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化录屏辅助类
        mScreenRecordHelper = ScreenRecordHelper.getInstance(this);
        initScreenRecordPermission();
    }
    
    private void initScreenRecordPermission() {
        if (mScreenRecordHelper.isScreenRecordSupported()) {
            if (!mScreenRecordHelper.hasScreenRecordPermission()) {
                Intent permissionIntent = mScreenRecordHelper.requestScreenRecordPermission(this);
                if (permissionIntent != null) {
                    startActivityForResult(permissionIntent, ScreenRecordHelper.REQUEST_CODE_SCREEN_CAPTURE);
                }
            }
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == ScreenRecordHelper.REQUEST_CODE_SCREEN_CAPTURE) {
            boolean success = mScreenRecordHelper.handlePermissionResult(resultCode, data);
            if (success) {
                Log.i(TAG, "录屏权限获取成功");
            }
        }
    }
}
```

### 3. 在测试脚本中使用

#### 方法1：使用ScreenRecordAction

```java
// 测试步骤配置示例
{
    "step": "开始录屏",
    "action": "ScreenRecord",
    "params": {
        "action": "start",
        "duration": "30",
        "fileName": "test_recording"
    }
}

{
    "step": "停止录屏", 
    "action": "ScreenRecord",
    "params": {
        "action": "stop"
    }
}
```

#### 方法2：直接使用ScreenRecordHelper

```java
ScreenRecordHelper helper = ScreenRecordHelper.getInstance(context);

// 开始录屏
boolean success = helper.startRecording("test.mp4", 30);

// 执行测试操作...

// 停止录屏
helper.stopRecording();
```

### 4. 编程方式使用

```java
public class TestRecordingExample {
    
    public void recordTestExecution() {
        ScreenRecordAction action = new ScreenRecordAction();
        
        try {
            // 开始录屏
            ActionContext startContext = new ActionContext();
            startContext.putParam("action", "start");
            startContext.putParam("duration", "60");
            startContext.putParam("fileName", "my_test_recording");
            
            TestResult startResult = action.execute(startContext);
            if (startResult.isSuccess()) {
                System.out.println("录屏开始成功");
                
                // 执行你的测试逻辑
                performTestSteps();
                
                // 停止录屏
                ActionContext stopContext = new ActionContext();
                stopContext.putParam("action", "stop");
                
                TestResult stopResult = action.execute(stopContext);
                System.out.println("录屏结果: " + stopResult.getMessage());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void performTestSteps() {
        // 你的测试步骤
    }
}
```

## API参考

### ScreenRecordAction参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| action | String | 是 | "start" 或 "stop" |
| duration | String | 否 | 录制时长（秒），默认30秒 |
| fileName | String | 否 | 输出文件名，默认自动生成 |

### ScreenRecordHelper方法

| 方法 | 说明 |
|------|------|
| `isScreenRecordSupported()` | 检查设备是否支持录屏 |
| `hasScreenRecordPermission()` | 检查是否有录屏权限 |
| `requestScreenRecordPermission()` | 请求录屏权限 |
| `startRecording()` | 开始录屏 |
| `stopRecording()` | 停止录屏 |
| `isRecording()` | 检查是否正在录制 |
| `getCurrentOutputPath()` | 获取当前录制文件路径 |

## 文件输出

录屏文件默认保存在：
```
/sdcard/AutoTest/video/
```

文件命名格式：
- 自动生成：`screen_record_<timestamp>.mp4`
- 指定名称：`<fileName>.mp4`

## 错误处理

### 常见错误及解决方案

1. **权限被拒绝**
   ```
   解决：重新请求MediaProjection权限
   ```

2. **设备不支持**
   ```
   解决：检查Android版本，使用shell命令作为fallback
   ```

3. **重复开始录屏**
   ```
   解决：先停止当前录屏再开始新的录屏
   ```

4. **存储空间不足**
   ```
   解决：清理旧的录屏文件或检查存储权限
   ```

### 错误处理示例

```java
try {
    boolean success = helper.startRecording("test.mp4", 30);
    if (!success) {
        if (!helper.hasScreenRecordPermission()) {
            // 重新请求权限
            requestPermissionAgain();
        } else if (helper.isRecording()) {
            // 停止当前录屏后重试
            helper.stopRecording();
            helper.startRecording("test.mp4", 30);
        }
    }
} catch (Exception e) {
    Log.e(TAG, "录屏失败", e);
    // 使用fallback方案或报告错误
}
```

## 性能考虑

1. **内存使用**：录屏会占用额外内存，建议在测试完成后及时停止
2. **存储空间**：长时间录屏会产生大文件，注意清理
3. **CPU占用**：录屏会增加CPU负载，可能影响测试性能
4. **电池消耗**：录屏会增加电池消耗

## 最佳实践

1. **及时停止录屏**：测试完成后立即停止录屏
2. **合理设置时长**：避免无限制录屏
3. **文件管理**：定期清理旧的录屏文件
4. **权限检查**：在录屏前检查权限状态
5. **错误处理**：提供完善的错误处理和fallback机制

## 示例代码

完整的使用示例请参考：
- `ScreenRecordDemo.java` - 演示各种使用场景
- `MainActivity.java` - 权限处理示例
- `ScreenRecordAction.java` - Action实现

## 故障排除

如果遇到问题，请检查：

1. Android版本是否支持（建议5.0+）
2. 是否正确请求了MediaProjection权限
3. 存储权限是否正常
4. 设备存储空间是否充足
5. 查看日志中的详细错误信息

更多技术支持请查看项目文档或联系开发团队。
