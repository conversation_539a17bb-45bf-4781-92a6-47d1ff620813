package com.xiaopeng.xpautotest.helper;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.media.projection.MediaProjectionManager;
import android.os.Build;

import com.xiaopeng.executor.action.accessibility.ScreenRecordAction;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.service.ScreenRecordManager;

/**
 * 屏幕录制辅助类
 * 处理录屏权限请求和管理
 */
public class ScreenRecordHelper {
    private static final String TAG = "ScreenRecordHelper";
    private static final String PREF_NAME = "screen_record_prefs";
    private static final String KEY_PERMISSION_GRANTED = "permission_granted";
    
    public static final int REQUEST_CODE_SCREEN_CAPTURE = 1001;
    
    private static ScreenRecordHelper sInstance;
    private Context mContext;
    private ScreenRecordManager mRecordManager;
    private SharedPreferences mPrefs;
    
    private ScreenRecordHelper(Context context) {
        mContext = context.getApplicationContext();
        mPrefs = mContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }
    
    public static synchronized ScreenRecordHelper getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new ScreenRecordHelper(context);
        }
        return sInstance;
    }
    
    /**
     * 检查是否支持录屏功能
     */
    public boolean isScreenRecordSupported() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            return true; // MediaProjection API支持
        }
        
        // 检查shell命令支持
        return ScreenRecordAction.isShellScreenRecordSupported();
    }
    
    /**
     * 检查是否已经获得录屏权限
     */
    public boolean hasScreenRecordPermission() {
        return mPrefs.getBoolean(KEY_PERMISSION_GRANTED, false) && mRecordManager != null;
    }
    
    /**
     * 请求录屏权限
     * @param activity 当前Activity
     * @return 权限请求Intent，如果不需要权限则返回null
     */
    public Intent requestScreenRecordPermission(Activity activity) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            FileLogger.w(TAG, "MediaProjection not supported, will use shell command");
            return null;
        }
        
        if (hasScreenRecordPermission()) {
            FileLogger.i(TAG, "Screen record permission already granted");
            return null;
        }
        
        try {
            MediaProjectionManager projectionManager = (MediaProjectionManager) 
                mContext.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
            
            if (projectionManager != null) {
                Intent intent = projectionManager.createScreenCaptureIntent();
                FileLogger.i(TAG, "Created screen capture permission intent");
                return intent;
            } else {
                FileLogger.e(TAG, "MediaProjectionManager is null");
                return null;
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to create screen capture intent", e);
            return null;
        }
    }
    
    /**
     * 处理权限请求结果
     * @param resultCode 结果码
     * @param data 返回的Intent数据
     * @return 是否成功获得权限
     */
    public boolean handlePermissionResult(int resultCode, Intent data) {
        if (resultCode != Activity.RESULT_OK) {
            FileLogger.w(TAG, "Screen capture permission denied, resultCode: " + resultCode);
            return false;
        }
        
        try {
            // 初始化ScreenRecordManager
            mRecordManager = new ScreenRecordManager(mContext);
            mRecordManager.initMediaProjection(resultCode, data);
            
            // 设置到ScreenRecordAction
            ScreenRecordAction.setScreenRecordManager(mRecordManager);
            
            // 保存权限状态
            mPrefs.edit().putBoolean(KEY_PERMISSION_GRANTED, true).apply();
            
            FileLogger.i(TAG, "Screen record permission granted and manager initialized");
            return true;
            
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to handle permission result", e);
            return false;
        }
    }
    
    /**
     * 开始录屏
     * @param fileName 文件名（可选）
     * @param durationSeconds 录制时长（秒）
     * @return 是否成功开始录制
     */
    public boolean startRecording(String fileName, int durationSeconds) {
        if (!hasScreenRecordPermission()) {
            FileLogger.e(TAG, "No screen record permission");
            return false;
        }
        
        return mRecordManager.startRecording(fileName, durationSeconds);
    }
    
    /**
     * 停止录屏
     */
    public void stopRecording() {
        if (mRecordManager != null) {
            mRecordManager.stopRecording();
        }
    }
    
    /**
     * 检查是否正在录制
     */
    public boolean isRecording() {
        return mRecordManager != null && mRecordManager.isRecording();
    }
    
    /**
     * 获取当前录制文件路径
     */
    public String getCurrentOutputPath() {
        if (mRecordManager != null) {
            return mRecordManager.getCurrentOutputPath();
        }
        return null;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (mRecordManager != null) {
            mRecordManager.release();
            mRecordManager = null;
        }
        
        // 清除权限状态
        mPrefs.edit().putBoolean(KEY_PERMISSION_GRANTED, false).apply();
        
        // 清理ScreenRecordAction中的引用
        ScreenRecordAction.cleanup();
        
        FileLogger.i(TAG, "ScreenRecordHelper cleaned up");
    }
    
    /**
     * 获取录屏能力信息
     */
    public String getCapabilitiesInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Screen Record Capabilities:\n");
        info.append("- Supported: ").append(isScreenRecordSupported()).append("\n");
        info.append("- Permission granted: ").append(hasScreenRecordPermission()).append("\n");
        info.append("- Currently recording: ").append(isRecording()).append("\n");
        info.append("- Device capabilities: ").append(ScreenRecordAction.getRecordingCapabilities());
        
        return info.toString();
    }
    
    /**
     * 重置权限状态（用于测试或重新授权）
     */
    public void resetPermission() {
        cleanup();
        FileLogger.i(TAG, "Screen record permission reset");
    }
    
    /**
     * 获取ScreenRecordManager实例
     */
    public ScreenRecordManager getRecordManager() {
        return mRecordManager;
    }
}
