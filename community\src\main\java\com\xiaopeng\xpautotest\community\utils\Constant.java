package com.xiaopeng.xpautotest.community.utils;

import java.util.ArrayList;

public class Constant {
    // SD卡目录
    public static final String SDCARD_PATH = "/sdcard/"; // Environment.getExternalStorageDirectory().getPath()
    public static final String AUTOTEST_PATH = SDCARD_PATH + "AutoTest/";
    public static final String AUTOTEST_SCRIPT_PATH = AUTOTEST_PATH + "script/";
    public static final String AUTOTEST_TEMPLATE_ICON_PATH = AUTOTEST_SCRIPT_PATH + "files/";
    public static final String AUTOTEST_TEST_TASK_FILE = AUTOTEST_SCRIPT_PATH + "taskInfo.json";
    public static final String AUTOTEST_SCRIPT_ZIP_FILENAME = "test_scripts.zip";
    public static final String AUTOTEST_SCRIPT_ZIP_FILE = AUTOTEST_SCRIPT_PATH + AUTOTEST_SCRIPT_ZIP_FILENAME;
    public static final String AUTOTEST_SCRIPT_LIST_FILE = AUTOTEST_SCRIPT_PATH + "test_scripts.json";
    public static final String AUTOTEST_SCRIPT_PENDING_REPORT_FILE = AUTOTEST_SCRIPT_PATH + "pending_report.json";
    public static final String AUTOTEST_LOG_PATH = AUTOTEST_PATH + "log/";
    public static final String AUTOTEST_REPORT_PATH = AUTOTEST_PATH + "report/";
    public static final String AUTOTEST_IMAGE_PATH = AUTOTEST_PATH + "image/";
    public static final String AUTOTEST_VIDEO_PATH = AUTOTEST_PATH + "video/";
    public static final String AUTOTEST_RESULT_PATH = AUTOTEST_PATH + "result/";
    public static final String AUTOTEST_RESULT_FILE = AUTOTEST_RESULT_PATH + "test_results.json";
    // 环境配置文件
    public static final String ENV_CONFIG_FILE = AUTOTEST_PATH + "env.ini";

    // UIDump数据目录
    public static final String UIDUMP_DATA_PATH = AUTOTEST_PATH + "uidump/";

    // 大屏日志目录
    public static final String CDU_LOG_PATH = AUTOTEST_PATH + "cdu_log/";

    // 大屏日志源文件路径
    public static final String CDU_MAIN_LOG_PATH = "/data/Log/log0/main.txt";
    public static final String CDU_SYSTEM_LOG_PATH = "/data/Log/log0/system.txt";

    // 截图前缀
    public static final String SCREENSHOT_PREFIX = "SCREENSHOT_";
    // 截图后缀
    public static final String SCREENSHOT_SUFFIX = ".png";
    public static final String UIDUMP_PREFIX = "UIDUMP_";

    public static final String UIDUMP_SUFFIX = ".xml";


    public static final String EVENT_VALUE_DIRECTION = "direction";
    public static final String EVENT_VALUE_OFFSET = "offset";
    public static final String EVENT_VALUE_DIRECTION_UP = "up";
    public static final String EVENT_VALUE_DIRECTION_LEFT = "left";
    public static final String EVENT_VALUE_DIRECTION_RIGHT = "right";

    public static final String EXTRA_TEST_SCRIPTS = "extra_test_scripts";
    public static final String EXTRA_EXECUTION_ID = "extra_execution_id";
    public static final String EXTRA_TEST_SUITE_ID = "extra_test_suite_id";
    public static final String EXTRA_TEST_MODE = "extra_test_mode";
    public static final String EXTRA_TEST_DATA_LOAD_TAG = "extra_test_data_load_tag";
    public  static final String EXTRA_MANUAL_STOP_TASK_ID = "manual_stop_task_id";
    public static final String [] ACTION_NO_NEED_REPORT = {"delay"};

    public static final ArrayList<String> BLACK_ACTIVITY_NAMES = new ArrayList<String>() {{
        add("android.widget.LinearLayout");
        add("android.widget.FrameLayout");
        add("android.widget.RelativeLayout");
    }};

}
