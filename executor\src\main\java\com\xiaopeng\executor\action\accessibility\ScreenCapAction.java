package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;


public class ScreenCapAction extends BaseAction {
    private static final String TAG = "ScreenCapAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String timeStamp = context.getTimeStampString();
        String imgFileName = this.service.screenShotSyn(timeStamp);

        if (imgFileName != null && !imgFileName.isEmpty()) {
            return TestResult.success("Screen capture successfully.", new TestResult.ActionArtifacts(imgFileName,""));
        }
        return TestResult.failure("Screen captured failed");
    }
}
