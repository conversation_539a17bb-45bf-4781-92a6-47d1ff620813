package com.xiaopeng.xpautotest.service;

import android.content.Context;
import android.content.Intent;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.MediaRecorder;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Constant;

import java.io.File;
import java.io.IOException;

/**
 * 屏幕录制管理器
 * 使用MediaProjection API进行录屏，无需root权限
 */
public class ScreenRecordManager {
    private static final String TAG = "ScreenRecordManager";
    
    private Context mContext;
    private MediaProjectionManager mProjectionManager;
    private MediaProjection mMediaProjection;
    private MediaRecorder mMediaRecorder;
    private VirtualDisplay mVirtualDisplay;
    private boolean mIsRecording = false;
    
    // 录屏参数
    private int mScreenWidth;
    private int mScreenHeight;
    private int mScreenDensity;
    private String mOutputPath;
    
    public ScreenRecordManager(Context context) {
        mContext = context;
        mProjectionManager = (MediaProjectionManager) 
            context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        initScreenMetrics();
    }
    
    /**
     * 初始化屏幕参数
     */
    private void initScreenMetrics() {
        WindowManager windowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics metrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getRealMetrics(metrics);
        
        mScreenWidth = metrics.widthPixels;
        mScreenHeight = metrics.heightPixels;
        mScreenDensity = metrics.densityDpi;
        
        FileLogger.i(TAG, "Screen metrics: " + mScreenWidth + "x" + mScreenHeight + ", density: " + mScreenDensity);
    }
    
    /**
     * 初始化MediaProjection
     * @param resultCode 权限请求结果码
     * @param data 权限请求返回的Intent
     */
    public void initMediaProjection(int resultCode, Intent data) {
        if (mProjectionManager != null) {
            mMediaProjection = mProjectionManager.getMediaProjection(resultCode, data);
            FileLogger.i(TAG, "MediaProjection initialized");
        } else {
            FileLogger.e(TAG, "MediaProjectionManager is null");
        }
    }
    
    /**
     * 开始录屏
     * @param outputFileName 输出文件名（不包含路径）
     * @param durationSeconds 录制时长（秒）
     * @return 是否成功开始录制
     */
    public boolean startRecording(String outputFileName, int durationSeconds) {
        if (mIsRecording) {
            FileLogger.w(TAG, "Already recording, stop current recording first");
            return false;
        }
        
        if (mMediaProjection == null) {
            FileLogger.e(TAG, "MediaProjection is null, cannot start recording");
            return false;
        }
        
        try {
            // 创建输出目录
            File outputDir = new File(Constant.AUTOTEST_VIDEO_PATH);
            if (!outputDir.exists() && !outputDir.mkdirs()) {
                FileLogger.e(TAG, "Failed to create output directory: " + outputDir.getAbsolutePath());
                return false;
            }
            
            // 生成完整输出路径
            if (outputFileName == null || outputFileName.isEmpty()) {
                outputFileName = "screen_record_" + System.currentTimeMillis() + ".mp4";
            }
            mOutputPath = new File(outputDir, outputFileName).getAbsolutePath();
            
            // 初始化MediaRecorder
            if (!initMediaRecorder(durationSeconds)) {
                return false;
            }
            
            // 创建VirtualDisplay
            mVirtualDisplay = mMediaProjection.createVirtualDisplay(
                "ScreenRecord",
                mScreenWidth, mScreenHeight, mScreenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                mMediaRecorder.getSurface(),
                null, null
            );
            
            // 开始录制
            mMediaRecorder.start();
            mIsRecording = true;
            
            FileLogger.i(TAG, "Screen recording started: " + mOutputPath + ", duration: " + durationSeconds + "s");
            
            // 设置定时停止
            if (durationSeconds > 0) {
                scheduleStopRecording(durationSeconds * 1000);
            }
            
            return true;
            
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to start recording", e);
            cleanup();
            return false;
        }
    }
    
    /**
     * 初始化MediaRecorder
     */
    private boolean initMediaRecorder(int durationSeconds) {
        try {
            mMediaRecorder = new MediaRecorder();
            mMediaRecorder.setVideoSource(MediaRecorder.VideoSource.SURFACE);
            mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4);
            mMediaRecorder.setVideoEncoder(MediaRecorder.VideoEncoder.H264);
            mMediaRecorder.setVideoSize(mScreenWidth, mScreenHeight);
            mMediaRecorder.setVideoFrameRate(30);
            mMediaRecorder.setVideoEncodingBitRate(5 * 1024 * 1024); // 5Mbps
            mMediaRecorder.setOutputFile(mOutputPath);
            
            // 设置最大录制时长（如果指定）
            if (durationSeconds > 0) {
                mMediaRecorder.setMaxDuration(durationSeconds * 1000);
                mMediaRecorder.setOnInfoListener((mr, what, extra) -> {
                    if (what == MediaRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED) {
                        FileLogger.i(TAG, "Max duration reached, stopping recording");
                        stopRecording();
                    }
                });
            }
            
            mMediaRecorder.prepare();
            return true;
            
        } catch (IOException e) {
            FileLogger.e(TAG, "Failed to prepare MediaRecorder", e);
            return false;
        }
    }
    
    /**
     * 停止录屏
     */
    public void stopRecording() {
        if (!mIsRecording) {
            FileLogger.w(TAG, "Not recording, nothing to stop");
            return;
        }
        
        try {
            if (mMediaRecorder != null) {
                mMediaRecorder.stop();
                FileLogger.i(TAG, "Screen recording stopped: " + mOutputPath);
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Error stopping MediaRecorder", e);
        } finally {
            cleanup();
            mIsRecording = false;
        }
    }
    
    /**
     * 定时停止录制
     */
    private void scheduleStopRecording(long delayMillis) {
        new Thread(() -> {
            try {
                Thread.sleep(delayMillis);
                if (mIsRecording) {
                    stopRecording();
                }
            } catch (InterruptedException e) {
                FileLogger.w(TAG, "Stop recording timer interrupted");
            }
        }).start();
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (mMediaRecorder != null) {
                mMediaRecorder.release();
                mMediaRecorder = null;
            }
            
            if (mVirtualDisplay != null) {
                mVirtualDisplay.release();
                mVirtualDisplay = null;
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Error during cleanup", e);
        }
    }
    
    /**
     * 释放所有资源
     */
    public void release() {
        stopRecording();
        
        if (mMediaProjection != null) {
            mMediaProjection.stop();
            mMediaProjection = null;
        }
        
        FileLogger.i(TAG, "ScreenRecordManager released");
    }
    
    /**
     * 检查是否正在录制
     */
    public boolean isRecording() {
        return mIsRecording;
    }
    
    /**
     * 获取当前录制文件路径
     */
    public String getCurrentOutputPath() {
        return mOutputPath;
    }
    
    /**
     * 创建录屏权限请求Intent
     */
    public Intent createScreenCaptureIntent() {
        if (mProjectionManager != null) {
            return mProjectionManager.createScreenCaptureIntent();
        }
        return null;
    }
}
