package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContext;
import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.io.IOException;
import java.util.Scanner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AdbClickAction extends BaseAction {
    private static final String TAG = "AdbClickAction";
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String coordinateStr = (String) context.getStringParam();
        if (coordinateStr == null) {
            throw new ActionException("coordinate is null!", FailureCode.SI001);
        }

        FileLogger.i("AdbClickAction", "params: " + coordinateStr);
        try (Scanner scanner = new Scanner(coordinateStr).useDelimiter("[^0-9]+")) {
            int x = scanner.nextInt();
            int y = scanner.nextInt();

            CMDUtils.CMD_Result rs = CMDUtils.runCMD("wm size", true, true);
            if (rs == null) {
                String errorMsg = "get wm size cmd result is null!";
                FileLogger.e(TAG, errorMsg);
                return TestResult.failure(errorMsg);
            }
            String s1 = rs.success.replaceFirst("Physical size: (\\d+)x(\\d+)", "$1");
            String s2 = rs.success.replaceFirst("Physical size: (\\d+)x(\\d+)", "$2");
            int mWidth = Integer.parseInt(s1.trim());
            int mHeight = Integer.parseInt(s2.trim());
            return shell("input tap " + mWidth * x / 100 + " " + mHeight * y / 100, null);
        } catch (Exception e) {
            FileLogger.e(TAG,e.getMessage());
            throw new ActionException(e.getMessage(), FailureCode.AB001, e);
        }
    }

    public TestResult shell(String command, String expectValue) throws IOException, InterruptedException {
        CMDUtils.CMD_Result cmdResult = CMDUtils.runCMD(command, true, true);
        if (cmdResult == null) {
            String errorMsg = "cmd result is null!";
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg);
        }
        if (cmdResult.resultCode != 0) {
            String errorMsg = "cmd result is error! " + cmdResult.error;
            FileLogger.e(TAG,errorMsg);
            return TestResult.failure(errorMsg);
        }
        if (expectValue != null && !expectValue.equals(getRealValue(cmdResult.success))) {
            String errorMsg = "cmd result is not equal! " + cmdResult.success;
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg);
        }

        String successMsg = "shell command success: " + cmdResult.success;
        FileLogger.d(TAG, successMsg);
        return TestResult.success(successMsg);
    }

    public String getRealValue(String input) {
        Pattern pattern = Pattern.compile("=\\s*(\\d+),");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
}
