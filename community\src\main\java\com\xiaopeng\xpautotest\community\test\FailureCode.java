package com.xiaopeng.xpautotest.community.test;

public enum FailureCode {
    // 业务功能问题 (BF)
    BF001("BF001", "车辆属性值验证失败", FailureCategory.BUSINESS_FUNCTION),
    BF002("BF002", "文本内容验证失败", FailureCategory.BUSINESS_FUNCTION),
    BF003("BF003", "UI元素状态不符合预期", FailureCategory.BUSINESS_FUNCTION),
    BF004("BF004", "UI元素未找到", FailureCategory.BUSINESS_FUNCTION),

    // 脚本问题 (SI)
    SI001("SI001", "参数缺失或格式错误", FailureCategory.SCRIPT_ISSUE),
    SI002("SI002", "坐标参数超出屏幕范围", FailureCategory.SCRIPT_ISSUE),
    SI003("SI003", "路径表达式格式错误", FailureCategory.SCRIPT_ISSUE),

    // 环境问题 (EI)
    EI001("EI001", "无障碍服务未启用", FailureCategory.ENVIRONMENT_ISSUE),
    EI002("EI002", "UI元素未找到或不可操作", FailureCategory.ENVIRONMENT_ISSUE),
    EI004("EI004", "应用启动失败", FailureCategory.ENVIRONMENT_ISSUE),
    EI005("EI005", "屏幕截图或UI转储失败", FailureCategory.ENVIRONMENT_ISSUE),
    EI006("EI006", "文件操作失败", FailureCategory.ENVIRONMENT_ISSUE),
    EI007("EI007", "权限不足", FailureCategory.ENVIRONMENT_ISSUE),
    EI008("EI008", "获取窗口根节点失败", FailureCategory.ENVIRONMENT_ISSUE),
    EI009("EI009", "获取LaunchIntent失败", FailureCategory.ENVIRONMENT_ISSUE),
    EI010("EI010", "获取Component失败", FailureCategory.ENVIRONMENT_ISSUE),
    EI011("EI011", "无障碍服务dispatchGesture失败", FailureCategory.ENVIRONMENT_ISSUE),

    // APK Bug (AB)
    AB001("AB001", "APK_BUG", FailureCategory.APK_BUG),
    AB002("AB002", "服务未实例化", FailureCategory.APK_BUG),
    AB003("AB003", "无障碍服务执行Action失败", FailureCategory.APK_BUG),
    AB004("AB004", "UI元素属性获取失败", FailureCategory.APK_BUG),
    AB005("AB005", "车辆属性值设置失败", FailureCategory.APK_BUG);

    private final String code;
    private final String description;
    private final FailureCategory category;
    
    FailureCode(String code, String description, FailureCategory category) {
        this.code = code;
        this.description = description;
        this.category = category;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public FailureCategory getCategory() {
        return category;
    }
}
